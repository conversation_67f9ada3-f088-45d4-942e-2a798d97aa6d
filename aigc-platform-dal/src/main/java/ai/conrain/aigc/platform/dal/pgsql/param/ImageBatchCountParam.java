package ai.conrain.aigc.platform.dal.pgsql.param;

import ai.conrain.aigc.platform.dal.example.ImageExample;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 图片批量统计参数
 * 用于动态构建查询字段的批量统计
 */
@Data
public class ImageBatchCountParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 查询条件
     */
    private ImageExample example;

    /**
     * 需要统计的字段配置列表
     */
    private List<MetadataFieldConfig> fieldConfigs;

    /**
     * 元数据字段配置
     */
    @Data
    public static class MetadataFieldConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 字段名称（metadata中的key）
         */
        private String fieldName;

        /**
         * 需要统计的值列表
         */
        private List<String> values;

        /**
         * 是否包含null值统计
         */
        private boolean includeNull = true;

        /**
         * 字段别名前缀（用于生成结果字段名）
         */
        private String aliasPrefix;

        public MetadataFieldConfig() {
        }

        public MetadataFieldConfig(String fieldName, List<String> values) {
            this.fieldName = fieldName;
            this.values = values;
            this.aliasPrefix = fieldName;
        }

        public MetadataFieldConfig(String fieldName, List<String> values, String aliasPrefix) {
            this.fieldName = fieldName;
            this.values = values;
            this.aliasPrefix = aliasPrefix;
        }
    }
}
