package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SceneImageCaptionJob extends JavaProcessor {

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionService imageCaptionService;

    private ExecutorService executor;

    @Autowired
    private SystemConfigService systemConfigService;

    @PostConstruct
    public void init() {
        executor = Executors.newFixedThreadPool(3);
    }

    @PreDestroy
    public void destroy() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
                log.info("[SceneImageCaptionJob] Thread pool shutdown completed");
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
                log.warn("[SceneImageCaptionJob] Thread pool shutdown interrupted", e);
            }
        }
    }

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        Integer count = systemConfigService.queryIntValue(SystemConstants.CAPTION_COUNT, 20);

        try {
            List<ImageVO> imageVOS = imageService.selectGeminiFlashCaptionedSceneImages4Embedding(count);
            if (CollectionUtils.isEmpty(imageVOS)) {
                log.info("[SceneImageCaptionJob] no uncaptioned scene images");
                return new ProcessResult(true);
            }

            // 使用CompletableFuture并发处理
            CompletableFuture[] futures = imageVOS.stream()
                    .map(imageVO -> CompletableFuture.runAsync(() -> {
                        String currentTraceId = uuid + "_" + imageVO.getId();
                        MDC.put("traceId", currentTraceId);
                        try {
                            imageCaptionService.createImageCaptionEmbeddings(imageVO, true, true);
                        } catch (Exception e) {
                            log.error("[SceneImageCaptionJob] error processing imageVO: {}", imageVO.getId(), e);
                        } finally {
                            MDC.remove("traceId");
                        }
                    }, executor))
                    .toArray(CompletableFuture[]::new);

            try {
                // 等待所有任务完成，最大超时时间
                CompletableFuture.allOf(futures).get(50, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("[SceneImageCaptionJob] error in concurrent processing", e);
            }

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
