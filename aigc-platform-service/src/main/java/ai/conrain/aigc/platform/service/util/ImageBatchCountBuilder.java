package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.dal.example.ImageExample;
import ai.conrain.aigc.platform.dal.pgsql.param.ImageBatchCountParam;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 图片批量统计构建器
 * 提供便捷的方法来构建动态批量统计参数
 *
 * <AUTHOR>
 */
public class ImageBatchCountBuilder {
    
    private final ImageBatchCountParam param;
    private final List<ImageBatchCountParam.MetadataFieldConfig> fieldConfigs;

    public ImageBatchCountBuilder() {
        this.param = new ImageBatchCountParam();
        this.fieldConfigs = new ArrayList<>();
        this.param.setFieldConfigs(fieldConfigs);
    }

    /**
     * 设置查询条件
     */
    public ImageBatchCountBuilder withExample(ImageExample example) {
        param.setExample(example);
        return this;
    }

    /**
     * 添加字段统计配置
     */
    public ImageBatchCountBuilder addField(String fieldName, List<String> values) {
        fieldConfigs.add(new ImageBatchCountParam.MetadataFieldConfig(fieldName, values));
        return this;
    }

    /**
     * 添加字段统计配置（带别名前缀）
     */
    public ImageBatchCountBuilder addField(String fieldName, List<String> values, String aliasPrefix) {
        fieldConfigs.add(new ImageBatchCountParam.MetadataFieldConfig(fieldName, values, aliasPrefix));
        return this;
    }

    /**
     * 添加字段统计配置（完整配置）
     */
    public ImageBatchCountBuilder addField(String fieldName, List<String> values, String aliasPrefix, boolean includeNull) {
        ImageBatchCountParam.MetadataFieldConfig config = 
            new ImageBatchCountParam.MetadataFieldConfig(fieldName, values, aliasPrefix);
        config.setIncludeNull(includeNull);
        fieldConfigs.add(config);
        return this;
    }

    /**
     * 添加布尔类型字段统计（yes/no）
     */
    public ImageBatchCountBuilder addBooleanField(String fieldName) {
        return addField(fieldName, Arrays.asList("yes", "no"));
    }

    /**
     * 添加布尔类型字段统计（yes/no，带别名）
     */
    public ImageBatchCountBuilder addBooleanField(String fieldName, String aliasPrefix) {
        return addField(fieldName, Arrays.asList("yes", "no"), aliasPrefix);
    }

    /**
     * 添加质量等级字段统计
     */
    public ImageBatchCountBuilder addQualityField() {
        return addField("quality", Arrays.asList("bad", "medium", "good"));
    }

    /**
     * 添加质量等级字段统计（带别名）
     */
    public ImageBatchCountBuilder addQualityField(String aliasPrefix) {
        return addField("quality", Arrays.asList("bad", "medium", "good"), aliasPrefix);
    }

    /**
     * 添加边框可用性字段统计
     */
    public ImageBatchCountBuilder addBorderedUsableField() {
        return addBooleanField("borderedUsable");
    }

    /**
     * 添加边框可用性字段统计（带别名）
     */
    public ImageBatchCountBuilder addBorderedUsableField(String aliasPrefix) {
        return addBooleanField("borderedUsable", aliasPrefix);
    }

    /**
     * 添加预期用途字段统计
     */
    public ImageBatchCountBuilder addIntendedUseField(List<String> usageTypes) {
        return addField("intendedUse", usageTypes);
    }

    /**
     * 添加预期用途字段统计（带别名）
     */
    public ImageBatchCountBuilder addIntendedUseField(List<String> usageTypes, String aliasPrefix) {
        return addField("intendedUse", usageTypes, aliasPrefix);
    }

    /**
     * 构建参数对象
     */
    public ImageBatchCountParam build() {
        return param;
    }

    /**
     * 创建新的构建器实例
     */
    public static ImageBatchCountBuilder create() {
        return new ImageBatchCountBuilder();
    }

    /**
     * 快速创建标准统计配置（包含质量和边框可用性）
     */
    public static ImageBatchCountParam createStandardStats() {
        return create()
            .addQualityField()
            .addBorderedUsableField()
            .build();
    }

    /**
     * 快速创建标准统计配置（带查询条件）
     */
    public static ImageBatchCountParam createStandardStats(ImageExample example) {
        return create()
            .withExample(example)
            .addQualityField()
            .addBorderedUsableField()
            .build();
    }
}
